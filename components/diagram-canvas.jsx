"use client"

import { use<PERSON><PERSON>back, useEffect, useMemo } from "react"
import { useSession } from "next-auth/react"
import React<PERSON>low, {
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  ConnectionLineType,
  Panel,
} from "reactflow"
import "reactflow/dist/style.css"
import { CustomNode } from "@/components/custom-node"
import { Button } from "@/components/ui/button"
import { RotateCcw, Save } from "lucide-react"
import useStore from "@/lib/store"
import { toast } from "sonner"

const nodeTypes = {
  custom: CustomNode,
}

const defaultEdgeOptions = {
  type: "smoothstep",
  animated: false,
  style: {
    stroke: "hsl(var(--primary))",
    strokeWidth: 2,
  },
}

export function DiagramCanvas() {
  const { data: session } = useSession()
  const {
    nodes: storeNodes,
    edges: storeEdges,
    initializeRootNode,
    loadUserDiagram,
    saveUserDiagram,
    clearDiagram,
  } = useStore()

  const [nodes, setNodes, onNodesChange] = useNodesState([])
  const [edges, setEdges, onEdgesChange] = useEdgesState([])

  // Sync store state with React Flow state
  useEffect(() => {
    setNodes(storeNodes)
    setEdges(storeEdges)
  }, [storeNodes, storeEdges, setNodes, setEdges])

  // Load user diagram on mount
  useEffect(() => {
    if (session?.user?.id) {
      loadUserDiagram(session.user.id)
    }
  }, [session?.user?.id, loadUserDiagram])

  // Initialize root node if no nodes exist
  useEffect(() => {
    if (session?.user?.id && storeNodes.length === 0) {
      initializeRootNode()
    }
  }, [session?.user?.id, storeNodes.length, initializeRootNode])

  const onConnect = useCallback(
    (params) => {
      // Prevent manual connections - connections are only created through the add node functionality
      return
    },
    []
  )

  const handleSave = useCallback(() => {
    if (session?.user?.id) {
      saveUserDiagram(session.user.id)
      toast.success("Diagram saved successfully!")
    }
  }, [session?.user?.id, saveUserDiagram])

  const handleReset = useCallback(() => {
    clearDiagram()
    initializeRootNode()
    toast.success("Diagram reset to root node")
  }, [clearDiagram, initializeRootNode])

  const proOptions = { hideAttribution: true }

  return (
    <div className="w-full h-full bg-background">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        defaultEdgeOptions={defaultEdgeOptions}
        connectionLineType={ConnectionLineType.SmoothStep}
        fitView
        fitViewOptions={{
          padding: 0.2,
          includeHiddenNodes: false,
        }}
        proOptions={proOptions}
        className="bg-background"
        // Disable edge interactions
        edgesUpdatable={false}
        edgesFocusable={false}
        edgesReconnectable={false}
        // Disable node dragging to maintain hierarchy
        nodesDraggable={false}
        nodesConnectable={false}
        nodesFocusable={true}
      >
        <Background 
          color="hsl(var(--muted-foreground))" 
          gap={20} 
          size={1}
          className="opacity-30"
        />
        
        <Controls 
          className="bg-background border border-border rounded-lg shadow-lg"
          showInteractive={false}
        />
        
        <MiniMap 
          className="bg-background border border-border rounded-lg shadow-lg"
          nodeColor="hsl(var(--primary))"
          maskColor="hsl(var(--muted) / 0.8)"
        />

        <Panel position="top-right" className="space-x-2">
          <Button
            onClick={handleSave}
            size="sm"
            className="shadow-lg"
          >
            <Save className="h-4 w-4 mr-2" />
            Save
          </Button>
          <Button
            onClick={handleReset}
            variant="outline"
            size="sm"
            className="shadow-lg"
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
        </Panel>
      </ReactFlow>
    </div>
  )
}
