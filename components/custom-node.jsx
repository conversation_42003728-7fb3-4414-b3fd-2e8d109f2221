"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON>, Position } from "reactflow"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Plus, Edit2, Check, X } from "lucide-react"
import useStore from "@/lib/store"

export function CustomNode({ data, id }) {
  const [isEditing, setIsEditing] = useState(false)
  const [editValue, setEditValue] = useState(data.label)
  const inputRef = useRef(null)
  const { updateNodeLabel } = useStore()

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus()
      inputRef.current.select()
    }
  }, [isEditing])

  const handleSave = () => {
    if (editValue.trim()) {
      updateNodeLabel(id, editValue.trim())
      setIsEditing(false)
    }
  }

  const handleCancel = () => {
    setEditValue(data.label)
    setIsEditing(false)
  }

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      handleSave()
    } else if (e.key === "Escape") {
      handleCancel()
    }
  }

  return (
    <div className="relative">
      {/* Input Handle (top) */}
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 !bg-primary border-2 border-background"
      />
      
      {/* Node Content */}
      <div className="bg-card border-2 border-border rounded-lg shadow-lg min-w-[200px] max-w-[300px]">
        <div className="p-4">
          {isEditing ? (
            <div className="space-y-2">
              <Input
                ref={inputRef}
                value={editValue}
                onChange={(e) => setEditValue(e.target.value)}
                onKeyDown={handleKeyDown}
                className="text-center font-medium"
                placeholder="Enter node name"
              />
              <div className="flex justify-center space-x-2">
                <Button size="sm" onClick={handleSave} disabled={!editValue.trim()}>
                  <Check className="h-3 w-3" />
                </Button>
                <Button size="sm" variant="outline" onClick={handleCancel}>
                  <X className="h-3 w-3" />
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center">
              <div className="flex items-center justify-center space-x-2 group">
                <span className="font-medium text-foreground select-none">
                  {data.label}
                </span>
                <Button
                  size="sm"
                  variant="ghost"
                  className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0"
                  onClick={() => setIsEditing(true)}
                >
                  <Edit2 className="h-3 w-3" />
                </Button>
              </div>
            </div>
          )}
        </div>
        
        {/* Add Child Button */}
        {!isEditing && (
          <div className="flex justify-center pb-2">
            <AddChildButton nodeId={id} />
          </div>
        )}
      </div>

      {/* Output Handle (bottom) */}
      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 !bg-primary border-2 border-background"
      />
    </div>
  )
}

function AddChildButton({ nodeId }) {
  const [showModal, setShowModal] = useState(false)
  const [newNodeName, setNewNodeName] = useState("")
  const inputRef = useRef(null)
  const { addNode } = useStore()

  useEffect(() => {
    if (showModal && inputRef.current) {
      inputRef.current.focus()
    }
  }, [showModal])

  const handleAddNode = () => {
    if (newNodeName.trim()) {
      addNode(nodeId, newNodeName.trim())
      setNewNodeName("")
      setShowModal(false)
    }
  }

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      handleAddNode()
    } else if (e.key === "Escape") {
      setNewNodeName("")
      setShowModal(false)
    }
  }

  if (showModal) {
    return (
      <div className="bg-background border rounded-lg p-3 shadow-lg min-w-[200px]">
        <div className="space-y-2">
          <Input
            ref={inputRef}
            value={newNodeName}
            onChange={(e) => setNewNodeName(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="New node name"
            className="text-sm"
          />
          <div className="flex justify-center space-x-2">
            <Button size="sm" onClick={handleAddNode} disabled={!newNodeName.trim()}>
              Save
            </Button>
            <Button 
              size="sm" 
              variant="outline" 
              onClick={() => {
                setNewNodeName("")
                setShowModal(false)
              }}
            >
              Cancel
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <Button
      size="sm"
      variant="outline"
      className="h-8 w-8 rounded-full p-0 border-dashed hover:border-solid transition-all"
      onClick={() => setShowModal(true)}
    >
      <Plus className="h-4 w-4" />
    </Button>
  )
}
