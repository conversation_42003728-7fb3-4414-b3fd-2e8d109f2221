import NextAuth from 'next-auth'
import Cred<PERSON><PERSON><PERSON>rovider from 'next-auth/providers/credentials'

const authOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        // For demo purposes, we'll use a simple in-memory user store
        // In a real app, you'd validate against a database
        const users = [
          { id: '1', email: '<EMAIL>', password: 'password123', name: 'Demo User' },
          { id: '2', email: '<EMAIL>', password: 'test123', name: 'Test User' }
        ]
        
        const user = users.find(u => u.email === credentials?.email)
        
        if (user && user.password === credentials?.password) {
          return {
            id: user.id,
            email: user.email,
            name: user.name
          }
        }
        return null
      }
    })
  ],
  pages: {
    signIn: '/',
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id
      }
      return token
    },
    async session({ session, token }) {
      session.user.id = token.id
      return session
    }
  },
  secret: process.env.NEXTAUTH_SECRET || 'your-secret-key'
}

const handler = NextAuth(authOptions)

export { handler as GET, handler as POST }
